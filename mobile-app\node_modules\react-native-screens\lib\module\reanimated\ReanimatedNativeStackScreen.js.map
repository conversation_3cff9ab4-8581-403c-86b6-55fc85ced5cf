{"version": 3, "names": ["React", "Platform", "InnerScreen", "Animated", "useEvent", "useSharedValue", "ReanimatedTransitionProgressContext", "AnimatedScreen", "createAnimatedComponent", "ENABLE_FABRIC", "_global", "global", "_IS_FABRIC", "ReanimatedNativeStackScreen", "forwardRef", "props", "ref", "children", "rest", "progress", "closing", "goingForward", "createElement", "_extends", "onTransitionProgressReanimated", "event", "value", "OS", "Provider", "displayName"], "sources": ["ReanimatedNativeStackScreen.tsx"], "sourcesContent": ["import React from 'react';\nimport { Platform } from 'react-native';\nimport {\n  InnerScreen,\n  ScreenProps,\n  TransitionProgressEventType,\n} from 'react-native-screens';\n\n// @ts-ignore file to be used only if `react-native-reanimated` available in the project\nimport Animated, { useEvent, useSharedValue } from 'react-native-reanimated';\nimport ReanimatedTransitionProgressContext from './ReanimatedTransitionProgressContext';\n\nconst AnimatedScreen = Animated.createAnimatedComponent(\n  InnerScreen as unknown as React.ComponentClass\n);\n\n// We use prop added to global by reanimated since it seems safer than the one from RN. See:\n// https://github.com/software-mansion/react-native-reanimated/blob/3fe8b35b05e82b2f2aefda1fb97799cf81e4b7bb/src/reanimated2/UpdateProps.ts#L46\n// @ts-expect-error nativeFabricUIManager is not yet included in the RN types\nconst ENABLE_FABRIC = !!global?._IS_FABRIC;\n\nconst ReanimatedNativeStackScreen = React.forwardRef<\n  typeof AnimatedScreen,\n  ScreenProps\n>((props, ref) => {\n  const { children, ...rest } = props;\n\n  const progress = useSharedValue(0);\n  const closing = useSharedValue(0);\n  const goingForward = useSharedValue(0);\n\n  return (\n    <AnimatedScreen\n      // @ts-ignore some problems with ref and onTransitionProgressReanimated being \"fake\" prop for parsing of `useEvent` return value\n      ref={ref}\n      onTransitionProgressReanimated={useEvent(\n        (event: TransitionProgressEventType) => {\n          'worklet';\n          progress.value = event.progress;\n          closing.value = event.closing;\n          goingForward.value = event.goingForward;\n        },\n        [\n          // This should not be necessary, but is not properly managed by `react-native-reanimated`\n          // @ts-ignore wrong type\n          Platform.OS === 'android'\n            ? 'onTransitionProgress'\n            : // for some reason there is a difference in required event name between architectures\n            ENABLE_FABRIC\n            ? 'onTransitionProgress'\n            : 'topTransitionProgress',\n        ]\n      )}\n      {...rest}\n    >\n      <ReanimatedTransitionProgressContext.Provider\n        value={{\n          progress: progress,\n          closing: closing,\n          goingForward: goingForward,\n        }}\n      >\n        {children}\n      </ReanimatedTransitionProgressContext.Provider>\n    </AnimatedScreen>\n  );\n});\n\nReanimatedNativeStackScreen.displayName = 'ReanimatedNativeStackScreen';\n\nexport default ReanimatedNativeStackScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,QAAQ,cAAc;AACvC,SACEC,WAAW,QAGN,sBAAsB;;AAE7B;AACA,OAAOC,QAAQ,IAAIC,QAAQ,EAAEC,cAAc,QAAQ,yBAAyB;AAC5E,OAAOC,mCAAmC,MAAM,uCAAuC;AAEvF,MAAMC,cAAc,GAAGJ,QAAQ,CAACK,uBAAuB,CACrDN,WACF,CAAC;;AAED;AACA;AACA;AACA,MAAMO,aAAa,GAAG,CAAC,GAAAC,OAAA,GAACC,MAAM,cAAAD,OAAA,eAANA,OAAA,CAAQE,UAAU;AAE1C,MAAMC,2BAA2B,gBAAGb,KAAK,CAACc,UAAU,CAGlD,CAACC,KAAK,EAAEC,GAAG,KAAK;EAChB,MAAM;IAAEC,QAAQ;IAAE,GAAGC;EAAK,CAAC,GAAGH,KAAK;EAEnC,MAAMI,QAAQ,GAAGd,cAAc,CAAC,CAAC,CAAC;EAClC,MAAMe,OAAO,GAAGf,cAAc,CAAC,CAAC,CAAC;EACjC,MAAMgB,YAAY,GAAGhB,cAAc,CAAC,CAAC,CAAC;EAEtC,oBACEL,KAAA,CAAAsB,aAAA,CAACf;EACC;EAAA,EAAAgB,QAAA;IACAP,GAAG,EAAEA,GAAI;IACTQ,8BAA8B,EAAEpB,QAAQ,CACrCqB,KAAkC,IAAK;MACtC,SAAS;;MACTN,QAAQ,CAACO,KAAK,GAAGD,KAAK,CAACN,QAAQ;MAC/BC,OAAO,CAACM,KAAK,GAAGD,KAAK,CAACL,OAAO;MAC7BC,YAAY,CAACK,KAAK,GAAGD,KAAK,CAACJ,YAAY;IACzC,CAAC,EACD;IACE;IACA;IACApB,QAAQ,CAAC0B,EAAE,KAAK,SAAS,GACrB,sBAAsB;IACtB;IACFlB,aAAa,GACX,sBAAsB,GACtB,uBAAuB,CAE/B;EAAE,GACES,IAAI,gBAERlB,KAAA,CAAAsB,aAAA,CAAChB,mCAAmC,CAACsB,QAAQ;IAC3CF,KAAK,EAAE;MACLP,QAAQ,EAAEA,QAAQ;MAClBC,OAAO,EAAEA,OAAO;MAChBC,YAAY,EAAEA;IAChB;EAAE,GAEDJ,QAC2C,CAChC,CAAC;AAErB,CAAC,CAAC;AAEFJ,2BAA2B,CAACgB,WAAW,GAAG,6BAA6B;AAEvE,eAAehB,2BAA2B"}