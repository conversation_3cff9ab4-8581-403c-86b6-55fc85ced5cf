{"version": 3, "names": ["React", "InnerScreen", "Animated", "AnimatedScreen", "createAnimatedComponent", "ReanimatedScreen", "forwardRef", "props", "ref", "createElement", "_extends", "displayName"], "sources": ["ReanimatedScreen.tsx"], "sourcesContent": ["import React from 'react';\nimport { InnerScreen, ScreenProps } from 'react-native-screens';\n\n// @ts-ignore file to be used only if `react-native-reanimated` available in the project\nimport Animated from 'react-native-reanimated';\n\nconst AnimatedScreen = Animated.createAnimatedComponent(\n  InnerScreen as unknown as React.ComponentClass\n);\n\nconst ReanimatedScreen = React.forwardRef<typeof AnimatedScreen, ScreenProps>(\n  (props, ref) => {\n    return (\n      <AnimatedScreen\n        // @ts-ignore some problems with ref and onTransitionProgressReanimated being \"fake\" prop for parsing of `useEvent` return value\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\n\nReanimatedScreen.displayName = 'ReanimatedScreen';\n\nexport default ReanimatedScreen;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAqB,sBAAsB;;AAE/D;AACA,OAAOC,QAAQ,MAAM,yBAAyB;AAE9C,MAAMC,cAAc,GAAGD,QAAQ,CAACE,uBAAuB,CACrDH,WACF,CAAC;AAED,MAAMI,gBAAgB,gBAAGL,KAAK,CAACM,UAAU,CACvC,CAACC,KAAK,EAAEC,GAAG,KAAK;EACd,oBACER,KAAA,CAAAS,aAAA,CAACN;EACC;EAAA,EAAAO,QAAA;IACAF,GAAG,EAAEA;EAAI,GACLD,KAAK,CACV,CAAC;AAEN,CACF,CAAC;AAEDF,gBAAgB,CAACM,WAAW,GAAG,kBAAkB;AAEjD,eAAeN,gBAAgB"}