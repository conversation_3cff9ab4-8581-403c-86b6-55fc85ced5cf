import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { apiService } from '../services/apiService';
import { AdvancedSearchBar } from '../components/SearchBar';
import { theme } from '../theme/theme';
import { useAuth } from '../context/AuthContext';

export default function ServicesScreen({ navigation }) {
  const { loading: authLoading, isAuthenticated, token, user } = useAuth();
  const [services, setServices] = useState([]);
  const [filteredServices, setFilteredServices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilters, setSelectedFilters] = useState([]);

  useEffect(() => {
    // Only load services when authentication is complete and user is authenticated
    if (!authLoading && isAuthenticated && token) {
      console.log('🔄 Auth state ready, loading services...');
      // Add a small delay to ensure token is properly set
      const timer = setTimeout(() => {
        loadServices();
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [authLoading, isAuthenticated, token]);

  useEffect(() => {
    filterServices();
  }, [services, searchQuery, selectedFilters]);

  const filterServices = () => {
    let filtered = [...services];

    // Apply text search
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(service =>
        service.title.toLowerCase().includes(query) ||
        service.description.toLowerCase().includes(query) ||
        (service.category && service.category.toLowerCase().includes(query))
      );
    }

    // Apply status filters
    if (selectedFilters.length > 0) {
      filtered = filtered.filter(service =>
        selectedFilters.includes(service.status)
      );
    }

    setFilteredServices(filtered);
  };

  const handleSearch = (query, filters) => {
    setSearchQuery(query);
    setSelectedFilters(filters);
  };

  const loadServices = async () => {
    try {
      setLoading(true);
      console.log('🔄 Loading services...');

      // Check if we have a token and ensure it's set in the API service
      if (user && !apiService.getAuthStatus().hasToken && token) {
        console.log('⚠️ User is logged in but API service has no token, attempting to restore...');
        console.log('🔑 Restoring token from AuthContext for services');
        apiService.setAuthToken(token);
      }

      const response = await apiService.getServicesWithCache();
      if (response.success) {
        console.log('✅ Services loaded successfully:', response.data.services?.length || response.data?.length || 0, 'services');
        setServices(response.data.services || response.data);
      } else {
        console.error('❌ Failed to load services:', response.message);
        Alert.alert('Error', 'Failed to load services');
      }
    } catch (error) {
      console.error('Error loading services:', error);
      Alert.alert('Error', 'Failed to load services');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadServices();
  };

  const getIconName = (iconClass) => {
    // Convert FontAwesome class to Ionicons name
    const iconMap = {
      'fas fa-tools': 'construct',
      'fas fa-hard-hat': 'shield',
      'fas fa-hammer': 'hammer',
      'fas fa-wrench': 'build',
      'fas fa-cogs': 'settings',
      'fas fa-building': 'business',
      'fas fa-home': 'home',
      'fas fa-industry': 'business',
      'fas fa-truck': 'car',
      'fas fa-tractor': 'car-sport',
      'fas fa-paint-roller': 'brush',
      'fas fa-ruler-combined': 'resize',
      'fas fa-drafting-compass': 'compass',
      'fas fa-layer-group': 'layers',
    };
    return iconMap[iconClass] || 'construct';
  };

  const renderService = ({ item }) => (
    <TouchableOpacity
      style={styles.serviceCard}
      onPress={() => navigation.navigate('ServiceDetail', { service: item })}
    >
      <View style={styles.serviceHeader}>
        <View style={styles.iconContainer}>
          <Ionicons
            name={getIconName(item.icon)}
            size={32}
            color={theme.colors.primary}
          />
        </View>
        <View style={styles.serviceInfo}>
          <Text style={styles.serviceTitle}>{item.title}</Text>
          <View style={[
            styles.statusBadge,
            { backgroundColor: item.status === 'active' ? theme.colors.success : theme.colors.placeholder }
          ]}>
            <Text style={styles.statusText}>{item.status}</Text>
          </View>
        </View>
      </View>
      <Text style={styles.serviceDescription} numberOfLines={3}>
        {item.description}
      </Text>
    </TouchableOpacity>
  );

  // Show loading while authentication is in progress
  if (authLoading) {
    return (
      <View style={styles.centerContainer}>
        <Text>Authenticating...</Text>
      </View>
    );
  }

  // Show loading while services are being fetched
  if (loading && !refreshing) {
    return (
      <View style={styles.centerContainer}>
        <Text>Loading services...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Services</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => navigation.navigate('AddService')}
        >
          <Ionicons name="add" size={24} color="white" />
        </TouchableOpacity>
      </View>

      {/* Search and Filter */}
      <View style={styles.searchContainer}>
        <AdvancedSearchBar
          placeholder="Search services..."
          onSearch={handleSearch}
          filters={[
            { label: 'Active', value: 'active', icon: 'checkmark-outline' },
            { label: 'Inactive', value: 'inactive', icon: 'close-outline' },
          ]}
          selectedFilters={selectedFilters}
        />
      </View>

      <FlatList
        data={filteredServices}
        renderItem={renderService}
        keyExtractor={(item) => item.id.toString()}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="construct-outline" size={64} color={theme.colors.placeholder} />
            <Text style={styles.emptyText}>No services found</Text>
            <TouchableOpacity
              style={styles.emptyButton}
              onPress={() => navigation.navigate('AddService')}
            >
              <Text style={styles.emptyButtonText}>Add First Service</Text>
            </TouchableOpacity>
          </View>
        }
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: theme.spacing.md,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  addButton: {
    backgroundColor: theme.colors.primary,
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchContainer: {
    backgroundColor: 'white',
    paddingHorizontal: theme.spacing.md,
    paddingBottom: theme.spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  listContainer: {
    padding: theme.spacing.md,
  },
  serviceCard: {
    backgroundColor: 'white',
    padding: theme.spacing.md,
    marginBottom: theme.spacing.md,
    borderRadius: theme.roundness,
    ...theme.shadows.small,
  },
  serviceHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: theme.spacing.md,
  },
  iconContainer: {
    width: 60,
    height: 60,
    backgroundColor: `${theme.colors.primary}20`,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: theme.spacing.md,
  },
  serviceInfo: {
    flex: 1,
    justifyContent: 'space-between',
  },
  serviceTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: theme.spacing.sm,
  },
  statusBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
    textTransform: 'capitalize',
  },
  serviceDescription: {
    fontSize: 14,
    color: theme.colors.text,
    lineHeight: 20,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 64,
  },
  emptyText: {
    fontSize: 18,
    color: theme.colors.placeholder,
    marginTop: theme.spacing.md,
    marginBottom: theme.spacing.lg,
  },
  emptyButton: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    borderRadius: theme.roundness,
  },
  emptyButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});
