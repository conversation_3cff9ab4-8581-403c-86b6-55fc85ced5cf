{"version": 3, "names": ["checkIfConfigurationExists", "project", "mode", "logger", "warn", "configurations", "includes", "CLIError", "join"], "sources": ["../../src/tools/checkIfConfigurationExists.ts"], "sourcesContent": ["import {CLIError, logger} from '@react-native-community/cli-tools';\nimport {IosProjectInfo} from '../types';\n\nexport function checkIfConfigurationExists(\n  project: IosProjectInfo,\n  mode: string,\n) {\n  if (!project) {\n    logger.warn(`Unable to check whether \"${mode}\" exists in your project`);\n    return;\n  }\n\n  if (!project.configurations.includes(mode)) {\n    throw new CLIError(\n      `Configuration \"${mode}\" does not exist in your project. Please use one of the existing configurations: ${project.configurations.join(\n        ', ',\n      )}`,\n    );\n  }\n}\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAGO,SAASA,0BAA0B,CACxCC,OAAuB,EACvBC,IAAY,EACZ;EACA,IAAI,CAACD,OAAO,EAAE;IACZE,kBAAM,CAACC,IAAI,CAAE,4BAA2BF,IAAK,0BAAyB,CAAC;IACvE;EACF;EAEA,IAAI,CAACD,OAAO,CAACI,cAAc,CAACC,QAAQ,CAACJ,IAAI,CAAC,EAAE;IAC1C,MAAM,KAAIK,oBAAQ,EACf,kBAAiBL,IAAK,oFAAmFD,OAAO,CAACI,cAAc,CAACG,IAAI,CACnI,IAAI,CACJ,EAAC,CACJ;EACH;AACF"}