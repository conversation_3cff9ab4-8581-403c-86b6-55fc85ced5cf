<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/auth.php';

$method = $_SERVER['REQUEST_METHOD'];
$user = requireAuth();

$database = new Database();
$conn = $database->getConnection();

switch ($method) {
    case 'GET':
        handleGetProjects();
        break;
    case 'POST':
        handleCreateProject();
        break;
    case 'PUT':
        handleUpdateProject();
        break;
    case 'DELETE':
        handleDeleteProject();
        break;
    default:
        jsonResponse(['success' => false, 'message' => 'Method not allowed'], 405);
}

function handleGetProjects() {
    global $conn;

    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
    $status = isset($_GET['status']) ? $_GET['status'] : null;
    $search = isset($_GET['search']) ? $_GET['search'] : null;
    
    $offset = ($page - 1) * $limit;
    
    // Build query
    $whereConditions = [];
    $params = [];
    
    if ($status) {
        $whereConditions[] = "status = ?";
        $params[] = $status;
    }
    
    if ($search) {
        $whereConditions[] = "(title LIKE ? OR description LIKE ? OR location LIKE ?)";
        $searchTerm = "%$search%";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }
    
    $whereClause = !empty($whereConditions) ? "WHERE " . implode(" AND ", $whereConditions) : "";
    
    // Get total count
    $countSql = "SELECT COUNT(*) as total FROM projects $whereClause";
    $stmt = $conn->prepare($countSql);
    $stmt->execute($params);
    $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // Get projects
    $sql = "SELECT * FROM projects $whereClause ORDER BY created_at DESC LIMIT $limit OFFSET $offset";
    $stmt = $conn->prepare($sql);
    $stmt->execute($params);
    $projects = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Process gallery field
    foreach ($projects as &$project) {
        if ($project['gallery']) {
            $project['gallery'] = json_decode($project['gallery'], true);
        } else {
            $project['gallery'] = [];
        }
    }
    
    jsonResponse([
        'success' => true,
        'data' => [
            'projects' => $projects,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => $total,
                'totalPages' => ceil($total / $limit)
            ]
        ]
    ]);
}

function handleCreateProject() {
    global $conn;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    // Validate required fields
    $requiredFields = ['title', 'description', 'location', 'status'];
    foreach ($requiredFields as $field) {
        if (!isset($input[$field]) || empty($input[$field])) {
            jsonResponse(['success' => false, 'message' => "Field '$field' is required"], 400);
        }
    }
    
    $title = sanitizeInput($input['title']);
    $description = sanitizeInput($input['description']);
    $location = sanitizeInput($input['location']);
    $client = isset($input['client']) ? sanitizeInput($input['client']) : null;
    $status = sanitizeInput($input['status']);
    $startDate = isset($input['start_date']) ? $input['start_date'] : null;
    $endDate = isset($input['end_date']) ? $input['end_date'] : null;
    $featuredImage = isset($input['featured_image']) ? $input['featured_image'] : null;
    $gallery = isset($input['gallery']) ? json_encode($input['gallery']) : null;
    
    // Validate status
    $validStatuses = ['completed', 'ongoing', 'planned'];
    if (!in_array($status, $validStatuses)) {
        jsonResponse(['success' => false, 'message' => 'Invalid status'], 400);
    }
    
    try {
        $sql = "INSERT INTO projects (title, description, location, client, start_date, end_date, status, featured_image, gallery) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$title, $description, $location, $client, $startDate, $endDate, $status, $featuredImage, $gallery]);
        
        $projectId = $conn->lastInsertId();
        
        // Get the created project
        $stmt = $conn->prepare("SELECT * FROM projects WHERE id = ?");
        $stmt->execute([$projectId]);
        $project = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($project['gallery']) {
            $project['gallery'] = json_decode($project['gallery'], true);
        }
        
        jsonResponse([
            'success' => true,
            'message' => 'Project created successfully',
            'data' => ['project' => $project]
        ]);
        
    } catch (PDOException $e) {
        jsonResponse(['success' => false, 'message' => 'Database error: ' . $e->getMessage()], 500);
    }
}

function handleUpdateProject() {
    global $conn;
    
    $projectId = isset($_GET['id']) ? (int)$_GET['id'] : 0;
    if (!$projectId) {
        jsonResponse(['success' => false, 'message' => 'Project ID required'], 400);
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    // Check if project exists
    $stmt = $conn->prepare("SELECT id FROM projects WHERE id = ?");
    $stmt->execute([$projectId]);
    if (!$stmt->fetch()) {
        jsonResponse(['success' => false, 'message' => 'Project not found'], 404);
    }
    
    // Build update query dynamically
    $updateFields = [];
    $params = [];
    
    $allowedFields = ['title', 'description', 'location', 'client', 'start_date', 'end_date', 'status', 'featured_image'];
    
    foreach ($allowedFields as $field) {
        if (isset($input[$field])) {
            $updateFields[] = "$field = ?";
            $params[] = $field === 'status' ? sanitizeInput($input[$field]) : $input[$field];
        }
    }
    
    if (isset($input['gallery'])) {
        $updateFields[] = "gallery = ?";
        $params[] = json_encode($input['gallery']);
    }
    
    if (empty($updateFields)) {
        jsonResponse(['success' => false, 'message' => 'No fields to update'], 400);
    }
    
    // Validate status if provided
    if (isset($input['status'])) {
        $validStatuses = ['completed', 'ongoing', 'planned'];
        if (!in_array($input['status'], $validStatuses)) {
            jsonResponse(['success' => false, 'message' => 'Invalid status'], 400);
        }
    }
    
    $params[] = $projectId;
    
    try {
        $sql = "UPDATE projects SET " . implode(', ', $updateFields) . ", updated_at = CURRENT_TIMESTAMP WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute($params);
        
        // Get updated project
        $stmt = $conn->prepare("SELECT * FROM projects WHERE id = ?");
        $stmt->execute([$projectId]);
        $project = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($project['gallery']) {
            $project['gallery'] = json_decode($project['gallery'], true);
        }
        
        jsonResponse([
            'success' => true,
            'message' => 'Project updated successfully',
            'data' => ['project' => $project]
        ]);
        
    } catch (PDOException $e) {
        jsonResponse(['success' => false, 'message' => 'Database error: ' . $e->getMessage()], 500);
    }
}

function handleDeleteProject() {
    global $conn;
    
    $projectId = isset($_GET['id']) ? (int)$_GET['id'] : 0;
    if (!$projectId) {
        jsonResponse(['success' => false, 'message' => 'Project ID required'], 400);
    }
    
    try {
        // Check if project exists
        $stmt = $conn->prepare("SELECT id, featured_image, gallery FROM projects WHERE id = ?");
        $stmt->execute([$projectId]);
        $project = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$project) {
            jsonResponse(['success' => false, 'message' => 'Project not found'], 404);
        }
        
        // Delete project
        $stmt = $conn->prepare("DELETE FROM projects WHERE id = ?");
        $stmt->execute([$projectId]);
        
        // TODO: Delete associated files
        // if ($project['featured_image']) {
        //     unlink($project['featured_image']);
        // }
        // if ($project['gallery']) {
        //     $gallery = json_decode($project['gallery'], true);
        //     foreach ($gallery as $image) {
        //         unlink($image);
        //     }
        // }
        
        jsonResponse([
            'success' => true,
            'message' => 'Project deleted successfully'
        ]);
        
    } catch (PDOException $e) {
        jsonResponse(['success' => false, 'message' => 'Database error: ' . $e->getMessage()], 500);
    }
}
?>
