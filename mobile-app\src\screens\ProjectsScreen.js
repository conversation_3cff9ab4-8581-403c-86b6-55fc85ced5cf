import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { apiService } from '../services/apiService';
import { AdvancedSearchBar } from '../components/SearchBar';
import { theme } from '../theme/theme';
import { useAuth } from '../context/AuthContext';

export default function ProjectsScreen({ navigation }) {
  const { loading: authLoading, isAuthenticated, token, user } = useAuth();
  const [projects, setProjects] = useState([]);
  const [filteredProjects, setFilteredProjects] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilters, setSelectedFilters] = useState([]);

  useEffect(() => {
    // Only load projects when authentication is complete and user is authenticated
    if (!authLoading && isAuthenticated && token) {
      console.log('🔄 Auth state ready, loading projects...');
      // Add a small delay to ensure token is properly set
      const timer = setTimeout(() => {
        loadProjects();
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [authLoading, isAuthenticated, token]);

  useEffect(() => {
    filterProjects();
  }, [projects, searchQuery, selectedFilters]);

  const filterProjects = () => {
    let filtered = [...projects];

    // Apply text search
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(project =>
        project.title.toLowerCase().includes(query) ||
        project.description.toLowerCase().includes(query) ||
        project.location.toLowerCase().includes(query) ||
        (project.client && project.client.toLowerCase().includes(query))
      );
    }

    // Apply status filters
    if (selectedFilters.length > 0) {
      filtered = filtered.filter(project =>
        selectedFilters.includes(project.status)
      );
    }

    setFilteredProjects(filtered);
  };

  const handleSearch = (query, filters) => {
    setSearchQuery(query);
    setSelectedFilters(filters);
  };

  const loadProjects = async () => {
    try {
      setLoading(true);
      console.log('🔄 Loading projects...');

      // Check if we have a token and ensure it's set in the API service
      if (user && !apiService.getAuthStatus().hasToken && token) {
        console.log('⚠️ User is logged in but API service has no token, attempting to restore...');
        console.log('🔑 Restoring token from AuthContext for projects');
        console.log('🔍 Token preview:', token.substring(0, 50) + '...');
        apiService.setAuthToken(token);
      }

      // Debug: Check current auth status before API call
      const authStatus = apiService.getAuthStatus();
      console.log('🔍 Auth status before projects API call:', authStatus);
      console.log('🔍 Full token being used:', authStatus.tokenPreview ? authStatus.tokenPreview.substring(0, 100) + '...' : 'null');

      const response = await apiService.getProjectsWithCache();
      if (response.success) {
        console.log('✅ Projects loaded successfully:', response.data.projects?.length || response.data?.length || 0, 'projects');
        setProjects(response.data.projects || response.data);
      } else {
        console.error('❌ Failed to load projects:', response.message);
        Alert.alert('Error', 'Failed to load projects');
      }
    } catch (error) {
      console.error('Error loading projects:', error);
      Alert.alert('Error', 'Failed to load projects');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadProjects();
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return theme.colors.success;
      case 'ongoing':
        return theme.colors.warning;
      case 'planned':
        return theme.colors.info;
      default:
        return theme.colors.placeholder;
    }
  };

  const renderProject = ({ item }) => (
    <TouchableOpacity
      style={styles.projectCard}
      onPress={() => navigation.navigate('ProjectDetail', { project: item })}
    >
      <View style={styles.projectHeader}>
        <Text style={styles.projectTitle}>{item.title}</Text>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
          <Text style={styles.statusText}>{item.status}</Text>
        </View>
      </View>
      <Text style={styles.projectLocation}>
        <Ionicons name="location-outline" size={14} color={theme.colors.placeholder} />
        {' '}{item.location}
      </Text>
      {item.client && (
        <Text style={styles.projectClient}>
          <Ionicons name="person-outline" size={14} color={theme.colors.placeholder} />
          {' '}{item.client}
        </Text>
      )}
      <Text style={styles.projectDescription} numberOfLines={2}>
        {item.description}
      </Text>
      {item.start_date && (
        <Text style={styles.projectDate}>
          <Ionicons name="calendar-outline" size={14} color={theme.colors.placeholder} />
          {' '}{new Date(item.start_date).toLocaleDateString()}
        </Text>
      )}
    </TouchableOpacity>
  );

  // Show loading while authentication is in progress
  if (authLoading) {
    return (
      <View style={styles.centerContainer}>
        <Text>Authenticating...</Text>
      </View>
    );
  }

  // Show loading while projects are being fetched
  if (loading && !refreshing) {
    return (
      <View style={styles.centerContainer}>
        <Text>Loading projects...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Projects</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => navigation.navigate('AddProject')}
        >
          <Ionicons name="add" size={24} color="white" />
        </TouchableOpacity>
      </View>

      {/* Search and Filter */}
      <View style={styles.searchContainer}>
        <AdvancedSearchBar
          placeholder="Search projects..."
          onSearch={handleSearch}
          filters={[
            { label: 'Planned', value: 'planned', icon: 'time-outline' },
            { label: 'Ongoing', value: 'ongoing', icon: 'play-outline' },
            { label: 'Completed', value: 'completed', icon: 'checkmark-outline' },
          ]}
          selectedFilters={selectedFilters}
        />
      </View>

      <FlatList
        data={filteredProjects}
        renderItem={renderProject}
        keyExtractor={(item) => item.id.toString()}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons name="business-outline" size={64} color={theme.colors.placeholder} />
            <Text style={styles.emptyText}>No projects found</Text>
            <TouchableOpacity
              style={styles.emptyButton}
              onPress={() => navigation.navigate('AddProject')}
            >
              <Text style={styles.emptyButtonText}>Add First Project</Text>
            </TouchableOpacity>
          </View>
        }
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: theme.spacing.md,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  addButton: {
    backgroundColor: theme.colors.primary,
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchContainer: {
    backgroundColor: 'white',
    paddingHorizontal: theme.spacing.md,
    paddingBottom: theme.spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  listContainer: {
    padding: theme.spacing.md,
  },
  projectCard: {
    backgroundColor: 'white',
    padding: theme.spacing.md,
    marginBottom: theme.spacing.md,
    borderRadius: theme.roundness,
    ...theme.shadows.small,
  },
  projectHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: theme.spacing.sm,
  },
  projectTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    flex: 1,
    marginRight: theme.spacing.sm,
  },
  statusBadge: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
    textTransform: 'capitalize',
  },
  projectLocation: {
    fontSize: 14,
    color: theme.colors.placeholder,
    marginBottom: 4,
  },
  projectClient: {
    fontSize: 14,
    color: theme.colors.placeholder,
    marginBottom: 4,
  },
  projectDescription: {
    fontSize: 14,
    color: theme.colors.text,
    lineHeight: 20,
    marginBottom: theme.spacing.sm,
  },
  projectDate: {
    fontSize: 12,
    color: theme.colors.placeholder,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 64,
  },
  emptyText: {
    fontSize: 18,
    color: theme.colors.placeholder,
    marginTop: theme.spacing.md,
    marginBottom: theme.spacing.lg,
  },
  emptyButton: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    borderRadius: theme.roundness,
  },
  emptyButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});
