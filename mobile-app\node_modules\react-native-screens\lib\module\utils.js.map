{"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "Platform", "isSearchBarAvailableForCurrentPlatform", "includes", "OS", "executeNativeBackPress", "exitApp", "isNewBackTitleImplementation"], "sources": ["utils.ts"], "sourcesContent": ["import { BackHandler, Platform } from 'react-native';\n\nexport const isSearchBarAvailableForCurrentPlatform = [\n  'ios',\n  'android',\n].includes(Platform.OS);\n\nexport function executeNativeBackPress() {\n  // This function invokes the native back press event\n  BackHandler.exitApp();\n  return true;\n}\n\n// Because of a bug introduced in https://github.com/software-mansion/react-native-screens/pull/1646\n// react-native-screens v3.21 changed how header's backTitle handles whitespace strings in https://github.com/software-mansion/react-native-screens/pull/1726\n// To allow for backwards compatibility in @react-navigation/native-stack we need a way to check if this version or newer is used.\n// See https://github.com/react-navigation/react-navigation/pull/11423 for more context.\nexport const isNewBackTitleImplementation = true;\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,QAAQ,QAAQ,cAAc;AAEpD,OAAO,MAAMC,sCAAsC,GAAG,CACpD,KAAK,EACL,SAAS,CACV,CAACC,QAAQ,CAACF,QAAQ,CAACG,EAAE,CAAC;AAEvB,OAAO,SAASC,sBAAsBA,CAAA,EAAG;EACvC;EACAL,WAAW,CAACM,OAAO,CAAC,CAAC;EACrB,OAAO,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA,OAAO,MAAMC,4BAA4B,GAAG,IAAI"}