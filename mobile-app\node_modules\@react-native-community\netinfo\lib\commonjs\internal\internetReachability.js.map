{"version": 3, "sources": ["internetReachability.ts"], "names": ["InternetReachability", "constructor", "configuration", "listener", "undefined", "isInternetReachable", "_isInternetReachable", "_listener", "expectsConnection", "_currentInternetReachabilityCheckHandler", "cancel", "_currentTimeoutHandle", "clearTimeout", "_configuration", "reachabilityShouldRun", "_setIsInternetReachable", "_checkInternetReachability", "responsePromise", "fetch", "reachabilityUrl", "method", "reachabilityMethod", "cache", "timeoutH<PERSON>le", "timeoutPromise", "Promise", "_", "reject", "setTimeout", "reachabilityRequestTimeout", "cancelPromise", "promise", "race", "then", "response", "reachabilityTest", "result", "nextTimeoutInterval", "reachabilityLongTimeout", "reachabilityShortTimeout", "catch", "error", "state", "useNativeReachability", "_setExpectsConnection", "isConnected"], "mappings": ";;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAUe,MAAMA,oBAAN,CAA2B;AAOxCC,EAAAA,WAAW,CACTC,aADS,EAETC,QAFS,EAGT;AAAA;;AAAA;;AAAA,kDAPyDC,SAOzD;;AAAA,sEAN0F,IAM1F;;AAAA,mDALoE,IAKpE;;AAAA,qDAMAC,mBADgC,IAEvB;AACT,UAAI,KAAKC,oBAAL,KAA8BD,mBAAlC,EAAuD;AACrD;AACD;;AAED,WAAKC,oBAAL,GAA4BD,mBAA5B;;AACA,WAAKE,SAAL,CAAe,KAAKD,oBAApB;AACD,KAdC;;AAAA,mDAgB+BE,iBAAD,IAA6C;AAC3E;AACA,UAAI,KAAKC,wCAAL,KAAkD,IAAtD,EAA4D;AAC1D,aAAKA,wCAAL,CAA8CC,MAA9C;;AACA,aAAKD,wCAAL,GAAgD,IAAhD;AACD,OAL0E,CAM3E;;;AACA,UAAI,KAAKE,qBAAL,KAA+B,IAAnC,EAAyC;AACvCC,QAAAA,YAAY,CAAC,KAAKD,qBAAN,CAAZ;AACA,aAAKA,qBAAL,GAA6B,IAA7B;AACD;;AAED,UAAIH,iBAAiB,IAAI,KAAKK,cAAL,CAAoBC,qBAApB,EAAzB,EAAsE;AACpE;AACA;AACA,YAAI,CAAC,KAAKR,oBAAV,EAAgC;AAC9B,eAAKS,uBAAL,CAA6B,IAA7B;AACD,SALmE,CAMpE;;;AACA,aAAKN,wCAAL,GAAgD,KAAKO,0BAAL,EAAhD;AACD,OARD,MAQO;AACL;AACA,aAAKD,uBAAL,CAA6B,KAA7B;AACD;AACF,KAxCC;;AAAA,wDA0CmC,MAAwC;AAC3E,YAAME,eAAe,GAAGC,KAAK,CAAC,KAAKL,cAAL,CAAoBM,eAArB,EAAsC;AACjEC,QAAAA,MAAM,EAAE,KAAKP,cAAL,CAAoBQ,kBADqC;AAEjEC,QAAAA,KAAK,EAAE;AAF0D,OAAtC,CAA7B,CAD2E,CAM3E;;AACA,UAAIC,aAAJ;AACA,YAAMC,cAAc,GAAG,IAAIC,OAAJ,CACrB,CAACC,CAAD,EAAIC,MAAJ,KAAqB;AACnBJ,QAAAA,aAAa,GAAGK,UAAU,CACxB,MAAYD,MAAM,CAAC,UAAD,CADM,EAExB,KAAKd,cAAL,CAAoBgB,0BAFI,CAA1B;AAID,OANoB,CAAvB,CAR2E,CAiB3E;AACA;;AACA,UAAInB,MAAkB,GAAG,MAAY,CAAE,CAAvC;;AACA,YAAMoB,aAAa,GAAG,IAAIL,OAAJ,CACpB,CAACC,CAAD,EAAIC,MAAJ,KAAqB;AACnBjB,QAAAA,MAAM,GAAG,MAAYiB,MAAM,CAAC,UAAD,CAA3B;AACD,OAHmB,CAAtB;AAMA,YAAMI,OAAO,GAAGN,OAAO,CAACO,IAAR,CAAa,CAC3Bf,eAD2B,EAE3BO,cAF2B,EAG3BM,aAH2B,CAAb,EAKbG,IALa,CAMXC,QAAD,IAAgC;AAC9B,eAAO,KAAKrB,cAAL,CAAoBsB,gBAApB,CAAqCD,QAArC,CAAP;AACD,OARW,EAUbD,IAVa,CAWXG,MAAD,IAAkB;AAChB,aAAKrB,uBAAL,CAA6BqB,MAA7B;;AACA,cAAMC,mBAAmB,GAAG,KAAK/B,oBAAL,GACxB,KAAKO,cAAL,CAAoByB,uBADI,GAExB,KAAKzB,cAAL,CAAoB0B,wBAFxB;AAGA,aAAK5B,qBAAL,GAA6BiB,UAAU,CACrC,KAAKZ,0BADgC,EAErCqB,mBAFqC,CAAvC;AAID,OApBW,EAsBbG,KAtBa,CAuBXC,KAAD,IAAkD;AAChD,YAAIA,KAAK,KAAK,UAAd,EAA0B;AACxB,eAAK1B,uBAAL,CAA6B,KAA7B;;AACA,eAAKJ,qBAAL,GAA6BiB,UAAU,CACrC,KAAKZ,0BADgC,EAErC,KAAKH,cAAL,CAAoB0B,wBAFiB,CAAvC;AAID;AACF,OA/BW,EAiCd;AAjCc,OAkCbN,IAlCa,CAmCZ,MAAY;AACVrB,QAAAA,YAAY,CAACW,aAAD,CAAZ;AACD,OArCW,EAsCXkB,KAAD,IAAwB;AACtB7B,QAAAA,YAAY,CAACW,aAAD,CAAZ;AACA,cAAMkB,KAAN;AACD,OAzCW,CAAhB;AA4CA,aAAO;AACLV,QAAAA,OADK;AAELrB,QAAAA;AAFK,OAAP;AAID,KApHC;;AAAA,oCAsHegC,KAAD,IAAwD;AACtE,UACE,OAAOA,KAAK,CAACrC,mBAAb,KAAqC,SAArC,IACA,KAAKQ,cAAL,CAAoB8B,qBAFtB,EAGE;AACA,aAAK5B,uBAAL,CAA6B2B,KAAK,CAACrC,mBAAnC;AACD,OALD,MAKO;AACL,aAAKuC,qBAAL,CAA2BF,KAAK,CAACG,WAAjC;AACD;AACF,KA/HC;;AAAA,0CAiIoB,MAAkC;AACtD,aAAO,KAAKvC,oBAAZ;AACD,KAnIC;;AAAA,sCAqIgB,MAAY;AAC5B;AACA,UAAI,KAAKG,wCAAL,KAAkD,IAAtD,EAA4D;AAC1D,aAAKA,wCAAL,CAA8CC,MAA9C;;AACA,aAAKD,wCAAL,GAAgD,IAAhD;AACD,OAL2B,CAO5B;;;AACA,UAAI,KAAKE,qBAAL,KAA+B,IAAnC,EAAyC;AACvCC,QAAAA,YAAY,CAAC,KAAKD,qBAAN,CAAZ;AACA,aAAKA,qBAAL,GAA6B,IAA7B;AACD;AACF,KAjJC;;AACA,SAAKE,cAAL,GAAsBX,aAAtB;AACA,SAAKK,SAAL,GAAiBJ,QAAjB;AACD;;AAbuC", "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n */\n\nimport * as PrivateTypes from './privateTypes';\nimport * as Types from './types';\n\ninterface InternetReachabilityCheckHandler {\n  promise: Promise<void>;\n  cancel: () => void;\n}\n\nexport default class InternetReachability {\n  private _configuration: Types.NetInfoConfiguration;\n  private _listener: PrivateTypes.NetInfoInternetReachabilityChangeListener;\n  private _isInternetReachable: boolean | null | undefined = undefined;\n  private _currentInternetReachabilityCheckHandler: InternetReachabilityCheckHandler | null = null;\n  private _currentTimeoutHandle: ReturnType<typeof setTimeout> | null = null;\n\n  constructor(\n    configuration: Types.NetInfoConfiguration,\n    listener: PrivateTypes.NetInfoInternetReachabilityChangeListener,\n  ) {\n    this._configuration = configuration;\n    this._listener = listener;\n  }\n\n  private _setIsInternetReachable = (\n    isInternetReachable: boolean | null,\n  ): void => {\n    if (this._isInternetReachable === isInternetReachable) {\n      return;\n    }\n\n    this._isInternetReachable = isInternetReachable;\n    this._listener(this._isInternetReachable);\n  };\n\n  private _setExpectsConnection = (expectsConnection: boolean | null): void => {\n    // Cancel any pending check\n    if (this._currentInternetReachabilityCheckHandler !== null) {\n      this._currentInternetReachabilityCheckHandler.cancel();\n      this._currentInternetReachabilityCheckHandler = null;\n    }\n    // Cancel any pending timeout\n    if (this._currentTimeoutHandle !== null) {\n      clearTimeout(this._currentTimeoutHandle);\n      this._currentTimeoutHandle = null;\n    }\n\n    if (expectsConnection && this._configuration.reachabilityShouldRun()) {\n      // If we expect a connection, start the process for finding if we have one\n      // Set the state to \"null\" if it was previously false\n      if (!this._isInternetReachable) {\n        this._setIsInternetReachable(null);\n      }\n      // Start a network request to check for internet\n      this._currentInternetReachabilityCheckHandler = this._checkInternetReachability();\n    } else {\n      // If we don't expect a connection or don't run reachability check, just change the state to \"false\"\n      this._setIsInternetReachable(false);\n    }\n  };\n\n  private _checkInternetReachability = (): InternetReachabilityCheckHandler => {\n    const responsePromise = fetch(this._configuration.reachabilityUrl, {\n      method: this._configuration.reachabilityMethod,\n      cache: 'no-cache',\n    });\n\n    // Create promise that will reject after the request timeout has been reached\n    let timeoutHandle: ReturnType<typeof setTimeout>;\n    const timeoutPromise = new Promise<Response>(\n      (_, reject): void => {\n        timeoutHandle = setTimeout(\n          (): void => reject('timedout'),\n          this._configuration.reachabilityRequestTimeout,\n        );\n      },\n    );\n\n    // Create promise that makes it possible to cancel a pending request through a reject\n    // eslint-disable-next-line @typescript-eslint/no-empty-function\n    let cancel: () => void = (): void => {};\n    const cancelPromise = new Promise<Response>(\n      (_, reject): void => {\n        cancel = (): void => reject('canceled');\n      },\n    );\n\n    const promise = Promise.race([\n      responsePromise,\n      timeoutPromise,\n      cancelPromise,\n    ])\n      .then(\n        (response): Promise<boolean> => {\n          return this._configuration.reachabilityTest(response);\n        },\n      )\n      .then(\n        (result): void => {\n          this._setIsInternetReachable(result);\n          const nextTimeoutInterval = this._isInternetReachable\n            ? this._configuration.reachabilityLongTimeout\n            : this._configuration.reachabilityShortTimeout;\n          this._currentTimeoutHandle = setTimeout(\n            this._checkInternetReachability,\n            nextTimeoutInterval,\n          );\n        },\n      )\n      .catch(\n        (error: Error | 'timedout' | 'canceled'): void => {\n          if (error !== 'canceled') {\n            this._setIsInternetReachable(false);\n            this._currentTimeoutHandle = setTimeout(\n              this._checkInternetReachability,\n              this._configuration.reachabilityShortTimeout,\n            );\n          }\n        },\n      )\n      // Clear request timeout and propagate any errors\n      .then(\n        (): void => {\n          clearTimeout(timeoutHandle);\n        },\n        (error: Error): void => {\n          clearTimeout(timeoutHandle);\n          throw error;\n        },\n      );\n\n    return {\n      promise,\n      cancel,\n    };\n  };\n\n  public update = (state: PrivateTypes.NetInfoNativeModuleState): void => {\n    if (\n      typeof state.isInternetReachable === 'boolean' &&\n      this._configuration.useNativeReachability\n    ) {\n      this._setIsInternetReachable(state.isInternetReachable);\n    } else {\n      this._setExpectsConnection(state.isConnected);\n    }\n  };\n\n  public currentState = (): boolean | null | undefined => {\n    return this._isInternetReachable;\n  };\n\n  public tearDown = (): void => {\n    // Cancel any pending check\n    if (this._currentInternetReachabilityCheckHandler !== null) {\n      this._currentInternetReachabilityCheckHandler.cancel();\n      this._currentInternetReachabilityCheckHandler = null;\n    }\n\n    // Cancel any pending timeout\n    if (this._currentTimeoutHandle !== null) {\n      clearTimeout(this._currentTimeoutHandle);\n      this._currentTimeoutHandle = null;\n    }\n  };\n}\n"]}