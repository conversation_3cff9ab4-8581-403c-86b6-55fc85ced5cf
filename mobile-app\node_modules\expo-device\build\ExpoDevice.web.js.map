{"version": 3, "file": "ExpoDevice.web.js", "sourceRoot": "", "sources": ["../src/ExpoDevice.web.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAC7C,OAAO,QAAQ,MAAM,cAAc,CAAC;AAEpC,OAAO,EAAE,UAAU,EAAE,MAAM,gBAAgB,CAAC;AAI5C,IAAI,MAAM,GAAQ,IAAI,CAAC;AACvB,IAAI,QAAQ,CAAC,cAAc,EAAE;IAC3B,MAAM,MAAM,GAAG,IAAI,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;IACxD,MAAM,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;CAC7B;AAED,SAAS,iBAAiB,CAAC,GAAW;IACpC,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC;AACrC,CAAC;AAED,SAAS,aAAa;IACpB,QAAQ,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE;QAC5B,KAAK,QAAQ;YACX,OAAO,UAAU,CAAC,KAAK,CAAC;QAC1B,KAAK,QAAQ;YACX,OAAO,UAAU,CAAC,MAAM,CAAC;QAC3B,KAAK,SAAS;YACZ,OAAO,UAAU,CAAC,EAAE,CAAC;QACvB,KAAK,SAAS,CAAC;QACf,KAAK,UAAU,CAAC;QAChB,KAAK,UAAU;YACb,OAAO,UAAU,CAAC,OAAO,CAAC;QAC5B;YACE,OAAO,UAAU,CAAC,OAAO,CAAC;KAC7B;AACH,CAAC;AAED,eAAe;IACb,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC;IACd,CAAC;IACD,IAAI,KAAK;QACP,OAAO,IAAI,CAAC;IACd,CAAC;IACD,IAAI,YAAY;QACd,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC;IAClD,CAAC;IACD,IAAI,SAAS;QACX,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC;IACjD,CAAC;IACD,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC;IACd,CAAC;IACD,IAAI,UAAU;QACZ,OAAO,aAAa,EAAE,CAAC;IACzB,CAAC;IACD,IAAI,WAAW;QACb,IAAI,QAAQ,CAAC,cAAc,IAAI,cAAc,IAAI,SAAS,EAAE;YAC1D,MAAM,EAAE,YAAY,EAAE,GAAG,SAAsC,CAAC;YAChE,OAAO,iBAAiB,CAAC,YAAY,CAAC,CAAC;SACxC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IACD,IAAI,yBAAyB;QAC3B,OAAO,MAAM,IAAI,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC9E,CAAC;IACD,IAAI,MAAM;QACR,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;IAC1C,CAAC;IACD,IAAI,SAAS;QACX,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;IAC7C,CAAC;IACD,IAAI,SAAS;QACX,OAAO,IAAI,CAAC;IACd,CAAC;IACD,IAAI,iBAAiB;QACnB,OAAO,IAAI,CAAC;IACd,CAAC;IACD,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC;IACd,CAAC;IACD,KAAK,CAAC,kBAAkB;QACtB,OAAO,aAAa,EAAE,CAAC;IACzB,CAAC;IACD,KAAK,CAAC,yBAAyB;QAC7B,OAAO,KAAK,CAAC;IACf,CAAC;CACF,CAAC", "sourcesContent": ["import { Platform } from 'expo-modules-core';\nimport UAParser from 'ua-parser-js';\n\nimport { DeviceType } from './Device.types';\n\ntype NavigatorWithDeviceMemory = Navigator & { deviceMemory: number };\n\nlet result: any = null;\nif (Platform.isDOMAvailable) {\n  const parser = new UAParser(window.navigator.userAgent);\n  result = parser.getResult();\n}\n\nfunction convertGiBtoBytes(gib: number): number {\n  return Math.round(gib * 1024 ** 3);\n}\n\nfunction getDeviceType(): DeviceType {\n  switch (result?.device?.type) {\n    case 'mobile':\n      return DeviceType.PHONE;\n    case 'tablet':\n      return DeviceType.TABLET;\n    case 'smarttv':\n      return DeviceType.TV;\n    case 'console':\n    case 'embedded':\n    case 'wearable':\n      return DeviceType.UNKNOWN;\n    default:\n      return DeviceType.DESKTOP;\n  }\n}\n\nexport default {\n  get isDevice(): boolean {\n    return true;\n  },\n  get brand(): null {\n    return null;\n  },\n  get manufacturer(): null {\n    return (result && result.device.vendor) || null;\n  },\n  get modelName(): string | null {\n    return (result && result.device.model) || null;\n  },\n  get deviceYearClass(): null {\n    return null;\n  },\n  get deviceType(): DeviceType {\n    return getDeviceType();\n  },\n  get totalMemory(): number | null {\n    if (Platform.isDOMAvailable && 'deviceMemory' in navigator) {\n      const { deviceMemory } = navigator as NavigatorWithDeviceMemory;\n      return convertGiBtoBytes(deviceMemory);\n    }\n    return null;\n  },\n  get supportedCpuArchitectures(): string[] | null {\n    return result && result.cpu.architecture ? [result.cpu.architecture] : null;\n  },\n  get osName(): string {\n    return (result && result.os.name) || '';\n  },\n  get osVersion(): string {\n    return (result && result.os.version) || '';\n  },\n  get osBuildId(): null {\n    return null;\n  },\n  get osInternalBuildId(): null {\n    return null;\n  },\n  get deviceName(): null {\n    return null;\n  },\n  async getDeviceTypeAsync(): Promise<DeviceType> {\n    return getDeviceType();\n  },\n  async isRootedExperimentalAsync(): Promise<boolean> {\n    return false;\n  },\n};\n"]}