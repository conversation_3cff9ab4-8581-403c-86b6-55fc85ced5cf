{"name": "@react-native-community/cli-server-api", "version": "11.3.10", "license": "MIT", "main": "build/index.js", "publishConfig": {"access": "public"}, "dependencies": {"@react-native-community/cli-debugger-ui": "11.3.10", "@react-native-community/cli-tools": "11.3.10", "compression": "^1.7.1", "connect": "^3.6.5", "errorhandler": "^1.5.1", "nocache": "^3.0.1", "pretty-format": "^26.6.2", "serve-static": "^1.13.1", "ws": "^7.5.1"}, "devDependencies": {"@types/compression": "^1.7.2", "@types/connect": "^3.4.33", "@types/errorhandler": "^1.5.0", "@types/ws": "^7.4.7"}, "files": ["build", "!*.map"], "homepage": "https://github.com/react-native-community/cli/tree/master/packages/cli-server-api", "repository": {"type": "git", "url": "https://github.com/react-native-community/cli.git", "directory": "packages/cli-server-api"}, "gitHead": "59e4dac7e56fb05f33508ff804c0eac7448c16a8"}