import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Keyboard,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { theme } from '../theme/theme';

export default function SearchBar({
  placeholder = 'Search...',
  onSearch,
  onClear,
  onFocus,
  onBlur,
  value = '',
  autoFocus = false,
  showClearButton = true,
  debounceMs = 300,
  style,
}) {
  const [searchText, setSearchText] = useState(value);
  const [isFocused, setIsFocused] = useState(false);
  const [debounceTimer, setDebounceTimer] = useState(null);

  const animatedOpacity = useState(new Animated.Value(0))[0];

  useEffect(() => {
    setSearchText(value);
  }, [value]);

  useEffect(() => {
    // Debounced search
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }

    const timer = setTimeout(() => {
      if (onSearch) {
        onSearch(searchText);
      }
    }, debounceMs);

    setDebounceTimer(timer);

    return () => {
      if (timer) {
        clearTimeout(timer);
      }
    };
  }, [searchText, debounceMs, onSearch]);

  useEffect(() => {
    // Animate search bar when focused
    Animated.timing(animatedOpacity, {
      toValue: isFocused || searchText ? 1 : 0,
      duration: 200,
      useNativeDriver: true, // Opacity can use native driver
    }).start();
  }, [isFocused, searchText, animatedOpacity]);

  const handleFocus = () => {
    setIsFocused(true);
    onFocus && onFocus();
  };

  const handleBlur = () => {
    setIsFocused(false);
    onBlur && onBlur();
  };

  const handleClear = () => {
    setSearchText('');
    onClear && onClear();
    Keyboard.dismiss();
  };

  const handleChangeText = (text) => {
    setSearchText(text);
  };



  return (
    <View style={[styles.container, style]}>
      <View style={styles.searchContainer}>
        <Ionicons
          name="search"
          size={20}
          color={isFocused ? theme.colors.primary : theme.colors.placeholder}
          style={styles.searchIcon}
        />

        <TextInput
          style={[
            styles.searchInput,
            isFocused && styles.searchInputFocused,
          ]}
          placeholder={placeholder}
          placeholderTextColor={theme.colors.placeholder}
          value={searchText}
          onChangeText={handleChangeText}
          onFocus={handleFocus}
          onBlur={handleBlur}
          autoFocus={autoFocus}
          returnKeyType="search"
          onSubmitEditing={() => onSearch && onSearch(searchText)}
        />

        {showClearButton && (searchText || isFocused) && (
          <Animated.View
            style={[
              styles.clearButtonContainer,
              {
                opacity: animatedOpacity,
              }
            ]}
          >
            <TouchableOpacity
              style={styles.clearButton}
              onPress={handleClear}
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            >
              <Ionicons
                name="close-circle"
                size={20}
                color={theme.colors.placeholder}
              />
            </TouchableOpacity>
          </Animated.View>
        )}
      </View>
    </View>
  );
}

// Advanced Search Bar with Filters
export function AdvancedSearchBar({
  onSearch,
  onFilterChange,
  filters = [],
  selectedFilters = [],
  placeholder = 'Search...',
  style,
}) {
  const [showFilters, setShowFilters] = useState(false);
  const [searchText, setSearchText] = useState('');

  const handleSearch = (text) => {
    setSearchText(text);
    onSearch && onSearch(text, selectedFilters);
  };

  const handleFilterToggle = (filter) => {
    const newFilters = selectedFilters.includes(filter)
      ? selectedFilters.filter(f => f !== filter)
      : [...selectedFilters, filter];

    onFilterChange && onFilterChange(newFilters);
    onSearch && onSearch(searchText, newFilters);
  };

  return (
    <View style={[styles.advancedContainer, style]}>
      <View style={styles.searchRow}>
        <SearchBar
          placeholder={placeholder}
          onSearch={handleSearch}
          style={styles.expandedSearch}
        />

        {filters.length > 0 && (
          <TouchableOpacity
            style={[
              styles.filterButton,
              showFilters && styles.filterButtonActive,
              selectedFilters.length > 0 && styles.filterButtonWithSelection,
            ]}
            onPress={() => setShowFilters(!showFilters)}
          >
            <Ionicons
              name="filter"
              size={20}
              color={showFilters || selectedFilters.length > 0 ? 'white' : theme.colors.primary}
            />
            {selectedFilters.length > 0 && (
              <View style={styles.filterBadge}>
                <Text style={styles.filterBadgeText}>{selectedFilters.length}</Text>
              </View>
            )}
          </TouchableOpacity>
        )}
      </View>

      {showFilters && filters.length > 0 && (
        <Animated.View style={styles.filtersContainer}>
          <View style={styles.filtersGrid}>
            {filters.map((filter, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  styles.filterChip,
                  selectedFilters.includes(filter.value) && styles.filterChipSelected,
                ]}
                onPress={() => handleFilterToggle(filter.value)}
              >
                {filter.icon && (
                  <Ionicons
                    name={filter.icon}
                    size={16}
                    color={selectedFilters.includes(filter.value) ? 'white' : theme.colors.primary}
                    style={styles.filterChipIcon}
                  />
                )}
                <Text style={[
                  styles.filterChipText,
                  selectedFilters.includes(filter.value) && styles.filterChipTextSelected,
                ]}>
                  {filter.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </Animated.View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginVertical: theme.spacing.sm,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: theme.roundness * 2,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    ...theme.shadows.small,
  },
  searchIcon: {
    marginRight: theme.spacing.sm,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: theme.colors.text,
    paddingVertical: theme.spacing.xs,
  },
  searchInputFocused: {
    color: theme.colors.text,
  },
  clearButtonContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
  },
  clearButton: {
    padding: theme.spacing.xs,
  },

  // Advanced Search Styles
  advancedContainer: {
    marginVertical: theme.spacing.sm,
  },
  searchRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  expandedSearch: {
    flex: 1,
    marginRight: theme.spacing.sm,
    marginVertical: 0,
  },
  filterButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.colors.primary,
    ...theme.shadows.small,
  },
  filterButtonActive: {
    backgroundColor: theme.colors.primary,
  },
  filterButtonWithSelection: {
    backgroundColor: theme.colors.primary,
  },
  filterBadge: {
    position: 'absolute',
    top: -4,
    right: -4,
    backgroundColor: theme.colors.error,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  filterBadgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  filtersContainer: {
    marginTop: theme.spacing.md,
    backgroundColor: 'white',
    borderRadius: theme.roundness,
    padding: theme.spacing.md,
    ...theme.shadows.small,
  },
  filtersGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  filterChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.roundness * 2,
    backgroundColor: '#f5f5f5',
    marginRight: theme.spacing.sm,
    marginBottom: theme.spacing.sm,
    borderWidth: 1,
    borderColor: theme.colors.primary,
  },
  filterChipSelected: {
    backgroundColor: theme.colors.primary,
  },
  filterChipIcon: {
    marginRight: theme.spacing.xs,
  },
  filterChipText: {
    fontSize: 14,
    color: theme.colors.primary,
    fontWeight: '500',
  },
  filterChipTextSelected: {
    color: 'white',
  },
});
