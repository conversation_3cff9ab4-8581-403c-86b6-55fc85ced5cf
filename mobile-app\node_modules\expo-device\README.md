<p>
  <a href="https://docs.expo.dev/versions/latest/sdk/device/">
    <img
      src="../../.github/resources/expo-device.svg"
      alt="expo-device"
      height="64" />
  </a>
</p>

Provides specific information about the device running the application.

# API documentation

- [Documentation for the main branch](https://github.com/expo/expo/blob/main/docs/pages/versions/unversioned/sdk/device.mdx)
- [Documentation for the latest stable release](https://docs.expo.dev/versions/latest/sdk/device/)

# Installation in managed Expo projects

For [managed](https://docs.expo.dev/archive/managed-vs-bare/) Expo projects, please follow the installation instructions in the [API documentation for the latest stable release](https://docs.expo.dev/versions/latest/sdk/device/).

# Installation in bare React Native projects

For bare React Native projects, you must ensure that you have [installed and configured the `expo` package](https://docs.expo.dev/bare/installing-expo-modules/) before continuing.

### Add the package to your npm dependencies

```
npx expo install expo-device
```

# Contributing

Contributions are very welcome! Please refer to guidelines described in the [contributing guide](https://github.com/expo/expo#contributing).
