{"version": 3, "sources": ["defaultConfiguration.ts"], "names": ["DEFAULT_CONFIGURATION", "reachabilityUrl", "reachabilityMethod", "reachabilityTest", "response", "Promise", "resolve", "status", "reachabilityShortTimeout", "reachabilityLongTimeout", "reachabilityRequestTimeout", "reachabilityShouldRun", "shouldFetchWiFiSSID", "useNativeReachability"], "mappings": ";;;;;;AAEA,MAAMA,qBAAiD,GAAG;AACxDC,EAAAA,eAAe,EAAE,0CADuC;AAExDC,EAAAA,kBAAkB,EAAE,MAFoC;AAGxDC,EAAAA,gBAAgB,EAAGC,QAAD,IAChBC,OAAO,CAACC,OAAR,CAAgBF,QAAQ,CAACG,MAAT,KAAoB,GAApC,CAJsD;AAKxDC,EAAAA,wBAAwB,EAAE,IAAI,IAL0B;AAKpB;AACpCC,EAAAA,uBAAuB,EAAE,KAAK,IAN0B;AAMpB;AACpCC,EAAAA,0BAA0B,EAAE,KAAK,IAPuB;AAOjB;AACvCC,EAAAA,qBAAqB,EAAE,MAAe,IARkB;AASxDC,EAAAA,mBAAmB,EAAE,KATmC;AAUxDC,EAAAA,qBAAqB,EAAE;AAViC,CAA1D;eAaeb,qB", "sourcesContent": ["import * as Types from './types';\n\nconst DEFAULT_CONFIGURATION: Types.NetInfoConfiguration = {\n  reachabilityUrl: 'https://clients3.google.com/generate_204',\n  reachabilityMethod: 'HEAD',\n  reachabilityTest: (response: Response): Promise<boolean> =>\n    Promise.resolve(response.status === 204),\n  reachabilityShortTimeout: 5 * 1000, // 5s\n  reachabilityLongTimeout: 60 * 1000, // 60s\n  reachabilityRequestTimeout: 15 * 1000, // 15s\n  reachabilityShouldRun: (): boolean => true,\n  shouldFetchWiFiSSID: false,\n  useNativeReachability: true\n};\n\nexport default DEFAULT_CONFIGURATION;"]}