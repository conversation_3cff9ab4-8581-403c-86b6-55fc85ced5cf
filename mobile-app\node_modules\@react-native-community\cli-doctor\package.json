{"name": "@react-native-community/cli-doctor", "version": "11.3.10", "license": "MIT", "main": "build/index.js", "publishConfig": {"access": "public"}, "types": "build/index.d.ts", "dependencies": {"@react-native-community/cli-config": "11.3.10", "@react-native-community/cli-platform-android": "11.3.10", "@react-native-community/cli-platform-ios": "11.3.10", "@react-native-community/cli-tools": "11.3.10", "chalk": "^4.1.2", "command-exists": "^1.2.8", "envinfo": "^7.7.2", "execa": "^5.0.0", "hermes-profile-transformer": "^0.0.6", "ip": "^1.1.5", "node-stream-zip": "^1.9.1", "ora": "^5.4.1", "prompts": "^2.4.0", "semver": "^7.5.2", "strip-ansi": "^5.2.0", "sudo-prompt": "^9.0.0", "wcwidth": "^1.0.1", "yaml": "^2.2.1"}, "files": ["build", "!*.d.ts", "!*.map"], "devDependencies": {"@react-native-community/cli-types": "11.3.10", "@types/command-exists": "^1.2.0", "@types/envinfo": "^7.8.1", "@types/ip": "^1.1.0", "@types/prompts": "^2.0.9", "@types/semver": "^6.0.2", "@types/wcwidth": "^1.0.0"}, "homepage": "https://github.com/react-native-community/cli/tree/master/packages/cli-doctor", "repository": {"type": "git", "url": "https://github.com/react-native-community/cli.git", "directory": "packages/cli-doctor"}, "gitHead": "59e4dac7e56fb05f33508ff804c0eac7448c16a8"}